<% content_for :before_main do %>
  <%= render AppBreadcrumbComponent.new(items: [
                                          { text: t("dashboard.index.title"), href: dashboard_path },
                                          { text: t("programmes.index.title"), href: programmes_path },
                                          { text: @programme.name, href: programme_vaccination_records_path(@programme) },
                                          { text: t("vaccination_records.index.title"), href: programme_vaccination_records_path(@programme) },
                                        ]) %>
<% end %>

<%= h1 @patient.full_name %>

<%= render AppPatientCardComponent.new(@patient) %>

<%= render AppCardComponent.new do |c| %>
  <% c.with_heading { "Vaccination details" } %>
  <%= render AppVaccinationRecordSummaryComponent.new(@vaccination_record, current_user:) %>

  <div class="app-button-group">
    <% if policy(@vaccination_record).edit? %>
      <%= govuk_button_to "Edit vaccination record",
                          programme_vaccination_record_path(@programme, @vaccination_record),
                          method: :put, secondary: true %>
    <% end %>

    <% if policy(@vaccination_record).destroy? %>
      <%= govuk_link_to "Delete vaccination record",
                        destroy_programme_vaccination_record_path(@programme, @vaccination_record) %>
    <% end %>
  </div>
<% end %>
