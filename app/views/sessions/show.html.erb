<% content_for :before_main do %>
  <%= render AppBreadcrumbComponent.new(items: [
                                          { text: t("dashboard.index.title"), href: dashboard_path },
                                          { text: t("sessions.index.title"), href: sessions_path },
                                        ]) %>
<% end %>

<%= render "header" %>

<div class="nhsuk-grid-row">
  <div class="nhsuk-grid-column-three-quarters">
    <%= render AppCardComponent.new do |card| %>
      <% card.with_heading { @session.clinic? ? "Community clinic" : "School session" } %>

      <p class="nhsuk-body"><%= session_status_tag(@session) %></p>

      <% if (dates = @session.dates).present? %>
        <h3 class="nhsuk-heading-s nhsuk-u-margin-bottom-2">Session dates</h3>
        <%= tag.ul(class: "nhsuk-list app-list--spaced") do %>
          <% dates.each do |date| %>
            <%= tag.li(date.to_fs(:long_day_of_week)) %>
          <% end %>
        <% end %>

        <p class="nhsuk-body">Consent period <%= session_consent_period(@session, in_sentence: true) %></p>
      <% end %>

      <h3 class="nhsuk-heading-s nhsuk-u-margin-bottom-2">Session details</h3>

      <%= render AppSessionDetailsSummaryComponent.new(@session) %>

      <% if @session.unscheduled? %>
        <% if policy(@session).edit? %>
          <%= govuk_button_link_to "Schedule sessions", edit_session_path(@session), secondary: true %>
        <% end %>
      <% else %>
        <%= render AppSessionActionsComponent.new(@session) %>

        <div class="app-button-group">
          <% if policy(@session).edit? %>
            <%= govuk_button_link_to "Edit session", edit_session_path(@session), secondary: true %>
          <% end %>

          <%= govuk_link_to "Record offline", session_path(@session, format: :xlsx) %>

          <% if @session.clinic? && @session.can_send_clinic_invitations? %>
            <%= govuk_link_to "Send booking reminders", edit_session_invite_to_clinic_path(@session) %>
          <% elsif @session.school? && @session.can_send_clinic_invitations? %>
            <%= govuk_link_to "Send clinic invitations", edit_session_invite_to_clinic_path(@session) %>
          <% end %>
        </div>
      <% end %>
    <% end %>
  </div>

  <div class="nhsuk-grid-column-one-quarter">
    <%= render AppSessionSummaryComponent.new(@session) %>
  </div>
</div>

<% content_for :after_main do %>
  <%= render(AppDevToolsComponent.new) do %>
    <p class="nhsuk-u-reading-width">
      Vaccinations can only be recorded if a session is in progress. If you want to test vaccination recording, you can set this session as in progress for today.
    </p>
    <%= govuk_button_to(
          "Set session in progress for today",
          make_in_progress_session_path,
          method: :put,
          secondary: true,
          prevent_double_click: true,
        ) %>
  <% end %>
<% end %>
