<%= render AppCardComponent.new(colour:) do |card| %>
  <% card.with_heading { heading } %>

  <% if consent_status.no_response? %>
    <% if latest_consent_request %>
      <p>No-one responded to our requests for consent.</p>
      <p>A request was sent on <%= latest_consent_request.sent_at.to_fs(:long) %>.</p>
    <% else %>
      <p>No requests have been sent.</p>
    <% end %>
  <% elsif consent_status.conflicts? %>
    <p>You can only vaccinate if all respondents give consent.</p>
  <% elsif consent_status.refused? %>
    <p><%= who_refused %> refused to give consent.</p>
  <% elsif consent_status.given? %>
    <p><%= patient.full_name %> is ready for the vaccinator.</p>
  <% end %>

  <div class="app-button-group nhsuk-u-margin-bottom-4">
    <% if can_send_consent_request? %>
      <%= govuk_button_to "Send consent request",
                          send_request_session_patient_programme_consents_path(
                            session, patient, programme
                          ),
                          secondary: true %>
    <% end %>

    <%= govuk_button_to "Get verbal consent",
                        session_patient_programme_consents_path(
                          session, patient, programme
                        ),
                        secondary: true %>
  </div>

  <%= render AppGillickAssessmentComponent.new(patient_session:, programme:) %>

  <%= render AppConsentTableComponent.new(patient_session:, programme:) %>
<% end %>
