<% if vaccination_records.present? %>
  <%= govuk_table(html_attributes: { class: "nhsuk-table-responsive" }) do |table| %>
    <% table.with_caption(text: "Vaccinations", size: "s") if show_caption %>

    <% table.with_head do |head| %>
      <% head.with_row do |row| %>
        <% row.with_cell(text: "Vaccination date") %>
        <% row.with_cell(text: "Location") %>
        <% row.with_cell(text: "Programme") if show_programme %>
        <% row.with_cell(text: "Outcome") %>
      <% end %>
    <% end %>

    <% table.with_body do |body| %>
      <% vaccination_records.each do |vaccination_record| %>
        <% body.with_row do |row| %>
          <% row.with_cell do %>
            <span class="nhsuk-table-responsive__heading">Vaccination date</span>
            <%= link_to vaccination_record.performed_at.to_date.to_fs(:long),
                        programme_vaccination_record_path(vaccination_record.programme, vaccination_record) %>
          <% end %>

          <% row.with_cell do %>
            <span class="nhsuk-table-responsive__heading">Location</span>
            <%= helpers.vaccination_record_location(vaccination_record) %>

            <% if (location = vaccination_record.location) && location.has_address? %>
              <br />
              <span class="nhsuk-u-secondary-text-color">
                <%= helpers.format_address_single_line(location) %>
              </span>
            <% end %>
          <% end %>

          <% if show_programme %>
            <% row.with_cell do %>
              <span class="nhsuk-table-responsive__heading">Programme</span>
              <%= render AppProgrammeTagsComponent.new([vaccination_record.programme]) %>
            <% end %>
          <% end %>

          <% row.with_cell do %>
            <span class="nhsuk-table-responsive__heading">Outcome</span>
            <%= helpers.vaccination_record_status_tag(vaccination_record) %>
          <% end %>
        <% end %>
      <% end %>
    <% end %>
  <% end %>
<% else %>
  <p class="nhsuk-body">No vaccinations</p>
<% end %>
