<h2 class="nhsuk-heading-m">Triage</h2>

<%= render AppCardComponent.new(colour:) do |card| %>
  <% card.with_heading { heading } %>

  <% if latest_triage.nil? || latest_triage.needs_follow_up? %>
    <p>You need to decide if it’s safe to vaccinate.</p>

    <% if triage_status&.consent_requires_triage? %>
      <p>Responses to health questions need triage.</p>
    <% end %>

    <% if triage_status&.vaccination_history_requires_triage? %>
      <p>Incomplete vaccination history for <%= programme.name %>. Check if the child needs another dose.</p>
    <% end %>

    <% if helpers.policy(Triage).new? %>
      <%= render AppTriageFormComponent.new(patient_session:, programme:, triage:, legend: :bold) %>
    <% end %>
  <% elsif latest_triage %>
    <% if latest_triage.ready_to_vaccinate? %>
      <p><%= latest_triage.performed_by.full_name %> decided that <%= patient.full_name %> is safe to vaccinate.</p>
    <% elsif latest_triage.do_not_vaccinate? %>
      <p><%= latest_triage.performed_by.full_name %> decided that <%= patient.full_name %> should not be vaccinated.</p>
    <% elsif latest_triage.do_not_vaccinate? %>
      <p><%= latest_triage.performed_by.full_name %> decided that <%= patient.full_name %>’s vaccination should be delayed.</p>
    <% end %>

    <div class="app-button-group nhsuk-u-margin-bottom-4">
      <% if helpers.policy(Triage).new? %>
        <%= govuk_button_link_to "Update triage outcome",
                                 new_session_patient_programme_triages_path(session, patient, programme),
                                 secondary: true %>
      <% end %>
    </div>
  <% end %>

  <%= render AppTriageTableComponent.new(patient_session:, programme:) %>
<% end %>
