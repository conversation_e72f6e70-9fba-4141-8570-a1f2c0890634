<%= govuk_table do |table| %>
  <% table.with_caption(text: "Consent responses", size: "s") %>

  <% table.with_head do |head| %>
    <% head.with_row do |row| %>
      <% row.with_cell(text: "Name") %>
      <% row.with_cell(text: "Response date") %>
      <% row.with_cell(text: "Decision") %>
    <% end %>
  <% end %>

  <% table.with_body do |body| %>
    <% consents.each do |consent| %>
      <% body.with_row do |row| %>
        <% classes = "app-table__cell-muted" if consent.invalidated? || consent.withdrawn? %>

        <% row.with_cell(classes:) do %>
          <%= link_to consent&.parent&.full_name || consent.patient.full_name,
                      session_patient_programme_consent_path(session, patient, programme, consent) %>
          <br />
          <span class="nhsuk-u-font-size-16"><%= consent.who_responded %></span>
        <% end %>

        <% row.with_cell(classes:, text: consent.responded_at.to_fs(:long)) %>

        <% row.with_cell(classes:, text: helpers.consent_status_tag(consent)) %>
      <% end %>
    <% end %>
  <% end %>
<% end %>
