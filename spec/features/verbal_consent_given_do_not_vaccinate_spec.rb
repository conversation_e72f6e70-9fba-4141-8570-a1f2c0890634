# frozen_string_literal: true

describe "Verbal consent" do
  scenario "Given, do not vaccinate" do
    given_i_am_signed_in

    when_i_record_that_verbal_consent_was_given_but_that_its_not_safe_to_vaccinate

    then_an_email_is_sent_to_the_parent_that_the_vaccination_wont_happen
    and_the_patients_status_is_do_not_vaccinate
  end

  def given_i_am_signed_in
    programmes = [create(:programme, :hpv)]
    organisation = create(:organisation, :with_one_nurse, programmes:)
    @session = create(:session, organisation:, programmes:)

    @parent = create(:parent)
    @patient = create(:patient, session: @session, parents: [@parent])

    sign_in organisation.users.first
  end

  def when_i_record_that_verbal_consent_was_given_but_that_its_not_safe_to_vaccinate
    visit session_consent_path(@session)
    click_link @patient.full_name
    click_button "Get verbal consent"

    # Who are you trying to get consent from?
    choose @parent.full_name
    click_button "Continue"

    # Details for parent or guardian: leave prepopulated details
    click_button "Continue"

    # How was the response given?
    choose "By phone"
    click_button "Continue"

    # Do they agree?
    choose "Yes, they agree"
    click_button "Continue"

    # Health questions
    find_all(".nhsuk-fieldset")[0].choose "No"
    find_all(".nhsuk-fieldset")[1].choose "No"
    find_all(".nhsuk-fieldset")[2].choose "Yes"
    find_all(".nhsuk-fieldset")[2].fill_in "Give details",
              with: "moar reactions"
    find_all(".nhsuk-fieldset")[3].choose "No"
    click_button "Continue"

    choose "No, do not vaccinate"
    click_button "Continue"

    # Confirm
    click_button "Confirm"

    expect(page).to have_content("Consent recorded for #{@patient.full_name}")
  end

  def and_the_patients_status_is_do_not_vaccinate
    click_link @patient.full_name, match: :first
    expect(page).to have_content("Do not vaccinate")
  end

  def then_an_email_is_sent_to_the_parent_that_the_vaccination_wont_happen
    expect_email_to(@parent.email, :triage_vaccination_wont_happen)
  end
end
